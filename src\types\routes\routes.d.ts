interface APIRoute {
    id: string;
    path: string;
    name: string;
    component: string;
    icon?: string;
    permissions?: string[];
    meta?: {
        title?: string;
        description?: string;
        layout?: string;
    };
    children?: APIRoute[];
    protected: boolean;
    order: number;
}

interface RouteModule {
    default: React.ComponentType<any>;
}

interface DynamicRouteConfig {
    route: APIRoute;
    component: React.ComponentType<any>;
    loading?: boolean;
    error?: string;
}