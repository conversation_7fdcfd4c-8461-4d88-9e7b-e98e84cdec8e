import React, { useState } from 'react'
import { Outlet } from 'react-router-dom'

const AuthLayout: React.FC = () => {
    const [activeSlide, setActiveSlide] = useState(0)

    const slides = [
        {
            title: "Building the Future...",
            description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."
        },
        {
            title: "Innovating Healthcare",
            description: "Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."
        },
        {
            title: "Empowering Professionals",
            description: "Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur."
        }
    ]
    return (
        <>
            <div className="min-h-screen flex" style={{
                backgroundImage: `linear-gradient(to right, rgba(20, 30, 48, 0.8), rgba(36, 59, 85, 0.8)), url("/img/bg-image.webp")`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
            }}>
                {/* Left Section - Hero */}
                <div className="hidden lg:flex lg:w-1/2 relative overflow-hidden">
                    {/* Logo */}
                    <div className="absolute top-8 left-8 z-10">
                        <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
                                <div className="w-6 h-6 bg-gradient-to-br from-stone-600 to-stone-800 rounded-sm flex items-center justify-center">
                                    <div className="w-3 h-3 bg-white rounded-full"></div>
                                </div>
                            </div>
                            <span className="text-white text-xl font-bold">Medistract</span>
                        </div>
                    </div>

                    {/* Hero Content */}
                    <div className="relative z-10 flex flex-col justify-center px-16 text-white">
                        <h1 className="text-5xl font-bold mb-6 leading-tight transition-all duration-500 ease-in-out">
                            {slides[activeSlide].title}
                        </h1>
                        <p className="text-xl text-stone-300 max-w-md leading-relaxed transition-all duration-500 ease-in-out">
                            {slides[activeSlide].description}
                        </p>

                        {/* Interactive Progress dots */}
                        <div className="flex space-x-2 mt-12">
                            {slides.map((_, index) => (
                                <button
                                    key={index}
                                    onClick={() => setActiveSlide(index)}
                                    className={`w-8 h-1 rounded-full transition-all duration-300 ease-in-out hover:scale-110 focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-transparent ${index === activeSlide
                                            ? 'bg-white'
                                            : 'bg-white/40 hover:bg-white/60'
                                        }`}
                                    aria-label={`Go to slide ${index + 1}`}
                                />
                            ))}
                        </div>
                    </div>
                </div>
                <Outlet />
            </div>
        </>
    )
}

export default AuthLayout