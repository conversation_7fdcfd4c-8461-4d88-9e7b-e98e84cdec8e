import React from 'react'
import { Outlet } from 'react-router-dom'

const AuthLayout: React.FC = () => {
    return (
        <>
            <div className="min-h-screen flex" style={{
                backgroundImage: `linear-gradient(to right, rgba(20, 30, 48, 0.8), rgba(36, 59, 85, 0.8)), url("/img/bg-image.webp")`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
            }}>
                {/* Left Section - Hero */}
                <div className="hidden lg:flex lg:w-1/2 relative overflow-hidden">
                    {/* Logo */}
                    <div className="absolute top-8 left-8 z-10">
                        <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
                                <div className="w-6 h-6 bg-gradient-to-br from-stone-600 to-stone-800 rounded-sm flex items-center justify-center">
                                    <div className="w-3 h-3 bg-white rounded-full"></div>
                                </div>
                            </div>
                            <span className="text-white text-xl font-bold">Medistract</span>
                        </div>
                    </div>

                    {/* Hero Content */}
                    <div className="relative z-10 flex flex-col justify-center px-16 text-white">
                        <h1 className="text-5xl font-bold mb-6 leading-tight">
                            Building the Future...
                        </h1>
                        <p className="text-xl text-stone-300 max-w-md leading-relaxed">
                            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                        </p>

                        {/* Progress dots */}
                        <div className="flex space-x-2 mt-12">
                            <div className="w-8 h-1 bg-white rounded-full"></div>
                            <div className="w-8 h-1 bg-white/40 rounded-full"></div>
                            <div className="w-8 h-1 bg-white/40 rounded-full"></div>
                        </div>
                    </div>
                </div>
                <Outlet />
            </div>
        </>
    )
}

export default AuthLayout