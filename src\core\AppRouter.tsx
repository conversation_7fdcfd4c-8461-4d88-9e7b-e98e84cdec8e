import React from 'react'
import { BrowserRouter, Routes, Route } from 'react-router-dom'
import { AuthRoutes, DashboardRoutes } from './constants/routes'

const AppRouter: React.FC = () => {
    return (
        <BrowserRouter>
            <Routes>
                <Route path='/*' element={AuthRoutes()} />
                <Route path='/*' element={DashboardRoutes()} />
                <Route path='/*' element={<div>Not Found 404</div>} />
            </Routes>
        </BrowserRouter>
    )
}

export default AppRouter