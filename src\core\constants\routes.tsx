import AuthLayout from "@/layouts/auth/AuthLayout"
import DashboardLayout from "@/layouts/dashboard/DashboardLayout"
import LoginPage from "@/pages/auth/LoginPage"
import RegisterPage from "@/pages/auth/RegisterPage"
import DashboardPage from "@/pages/dashboard/DashboardPage"
import { Routes, Route } from "react-router-dom"

export function DashboardRoutes() {
    return (
        <Routes>
            <Route path="/dashboard" element={<DashboardLayout />}>
                <Route index element={<DashboardPage />} />
            </Route>
        </Routes>
    )
}
export function AuthRoutes() {
    return (
        <Routes>
            <Route path="/auth" element={<AuthLayout />}>
                <Route index element={<LoginPage />} />
                <Route path="register" element={<RegisterPage />} />
            </Route>
        </Routes>
    )
}
