name: Deploy EKKO to AWS  (Development)

on:
  push:
    branches:
      - develop

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Set environment variables for Development
        run: |
          echo "VITE_API_URL=${{ secrets.DEV_API_URL }}" >> $GITHUB_ENV
          echo "VITE_ENV=development" >> $GITHUB_ENV
          echo "AWS_S3_BUCKET=${{ secrets.DEV_S3_BUCKET }}" >> $GITHUB_ENV
          echo "AWS_CLOUDFRONT_DISTRIBUTION_ID=${{ secrets.DEV_CLOUDFRONT_DISTRIBUTION_ID }}" >> $GITHUB_ENV

      - name: Build Vite app
        run: npm run build

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Deploy to S3
        run: |
          aws s3 sync dist/ s3://$AWS_S3_BUCKET --delete --cache-control max-age=31536000,public
          aws s3 cp s3://$AWS_S3_BUCKET/index.html s3://$AWS_S3_BUCKET/index.html --metadata-directive REPLACE --cache-control max-age=0,no-cache,no-store,must-revalidate --content-type text/html

      - name: Invalidate CloudFront cache
        run: |
          aws cloudfront create-invalidation --distribution-id $AWS_CLOUDFRONT_DISTRIBUTION_ID --paths "/*"

      - name: Deployment Status
        run: echo "🚀 Successfully deployed to DEVELOPMENT environment"