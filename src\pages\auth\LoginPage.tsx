import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Eye, EyeOff } from 'lucide-react';

const LoginPage: React.FC = () => {
    const [showPassword, setShowPassword] = useState(false);
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        password: ''
    });

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    };

    const handleSubmit = () => {
        console.log('Form submitted:', formData);
    };

    const handleSocialLogin = (provider: string) => {
        console.log(`${provider} login clicked`);
    };

    return (
        <>
            {/* Right Section - Sign Up Form */}
            <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
                <Card className="w-full max-w-md shadow-xl border-0">
                    <CardHeader className="pb-6">
                        <div className="text-sm text-gray-500 mb-2">LET'S GET YOU STARTED</div>
                        <CardTitle className="text-2xl font-bold text-gray-900">Create an Account</CardTitle>
                    </CardHeader>

                    <CardContent className="space-y-6">
                        <div className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="email" className="text-sm font-medium text-gray-700">Email</Label>
                                <Input
                                    id="email"
                                    name="email"
                                    type="email"
                                    placeholder="<EMAIL>"
                                    value={formData.email}
                                    onChange={handleInputChange}
                                    className="h-12 border-gray-300 focus:border-gray-900 focus:ring-gray-900"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="password" className="text-sm font-medium text-gray-700">Password</Label>
                                <div className="relative">
                                    <Input
                                        id="password"
                                        name="password"
                                        type={showPassword ? 'text' : 'password'}
                                        placeholder="••••••••••••••"
                                        value={formData.password}
                                        onChange={handleInputChange}
                                        className="h-12 border-gray-300 focus:border-gray-900 focus:ring-gray-900 pr-10"
                                    />
                                    <button
                                        onClick={() => setShowPassword(!showPassword)}
                                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                                    >
                                        {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                                    </button>
                                </div>
                                <div className="flex items-center justify-between pt-1">
                                    <label className="flex items-center space-x-2 text-sm text-gray-700">
                                        <input type="checkbox" className="form-checkbox h-4 w-4 text-gray-900" />
                                        <span>Remember me</span>
                                    </label>
                                    <button className="text-sm text-gray-700 hover:underline">
                                        Forgot password?
                                    </button>
                                </div>
                            </div>

                            <Button
                                onClick={handleSubmit}
                                className="w-full h-12 bg-gray-900 hover:bg-gray-800 text-white font-medium text-sm"
                            >
                                CONTINUE
                            </Button>
                        </div>

                        <div className="relative">
                            <div className="absolute inset-0 flex items-center">
                                <Separator className="w-full" />
                            </div>
                            <div className="relative flex justify-center text-sm">
                                <span className="bg-white px-4 text-gray-500">Or</span>
                            </div>
                        </div>

                        <div className="space-y-3">
                            <Button
                                variant="outline"
                                onClick={() => handleSocialLogin('Google')}
                                className="w-full h-12 border-gray-300 hover:bg-gray-50"
                            >
                                <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
                                    <path fill="#4285f4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                                    <path fill="#34a853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
                                    <path fill="#fbbc05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
                                    <path fill="#ea4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
                                </svg>
                                Sign up with Google
                            </Button>

                            {/* <Button
                                variant="outline"
                                onClick={() => handleSocialLogin('Facebook')}
                                className="w-full h-12 border-gray-300 hover:bg-gray-50"
                            >
                                <svg className="w-5 h-5 mr-3" fill="#1877f2" viewBox="0 0 24 24">
                                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                                </svg>
                                Sign up with Facebook
                            </Button>

                            <Button
                                variant="outline"
                                onClick={() => handleSocialLogin('Apple')}
                                className="w-full h-12 border-gray-300 hover:bg-gray-50"
                            >
                                <svg className="w-5 h-5 mr-3" fill="#000000" viewBox="0 0 24 24">
                                    <path d="M12.152 6.896c-.948 0-2.415-1.078-3.96-1.04-2.04.027-3.91 1.183-4.961 3.014-2.117 3.675-.546 9.103 1.519 12.09 1.013 1.454 2.208 3.09 3.792 3.039 1.52-.065 2.09-.987 3.935-.987 1.831 0 2.35.987 3.96.948 1.637-.026 2.676-1.48 3.676-2.948 1.156-1.688 1.636-3.325 1.662-3.415-.039-.013-3.182-1.221-3.22-4.857-.026-3.04 2.48-4.494 2.597-4.559-1.429-2.09-3.623-2.324-4.39-2.376-2-.156-3.675 1.09-4.61 1.09zM15.53 3.83c.843-1.012 1.4-2.427 1.245-3.83-1.207.052-2.662.805-3.532 1.818-.78.896-1.454 2.338-1.273 3.714 1.338.104 2.715-.688 3.559-1.701" />
                                </svg>
                                Sign up with Apple
                            </Button> */}
                        </div>

                        <div className="text-center text-sm text-gray-600">
                            New User?{' '}
                            <button className="font-medium text-gray-900 hover:underline">
                                SIGN UP HERE
                            </button>
                        </div>
                    </CardContent>
                </Card>
            </div></>
    );
};

export default LoginPage;