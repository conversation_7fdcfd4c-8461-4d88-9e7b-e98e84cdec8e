{"name": "coach-flow-fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "prepare": "husky"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.11", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/jsdom": "^21.1.7", "@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "@vitest/ui": "^3.2.4", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "tw-animate-css": "^1.3.4", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0", "vitest": "^3.2.4"}}