# CRM Platform Documentation

## Authentication HOCs

### withAuth
General authentication wrapper that can include permissions and roles.

```tsx
const ProtectedComponent = withAuth(MyComponent, {
  permissions: ['read', 'write'],
  roles: ['admin'],
  requireAllPermissions: true
});
```

### withPermissions
Permission-specific protection.

```tsx
const AdminOnlyComponent = withPermissions(MyComponent, {
  permissions: ['admin.read'],
  requireAll: true
});
```

### withRoles
Role-specific protection.

```tsx
const ManagerComponent = withRoles(MyComponent, {
  roles: ['manager', 'admin']
});
```

## Route Protection

```tsx
<ProtectedRoute permissions={['dashboard.read']}>
  <DashboardPage />
</ProtectedRoute>
```
